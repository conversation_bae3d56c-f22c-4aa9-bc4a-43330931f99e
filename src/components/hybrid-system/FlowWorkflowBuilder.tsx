"use client";

import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from 'react';
import {
  ReactFlow,
  ReactFlowProvider,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Node,
  Edge,
  Connection,
  ConnectionMode,

  useReactFlow,
  NodeTypes,
  EdgeTypes,
} from '@/lib/mock-reactflow';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Bot,
  Wrench,
  <PERSON>,
  Circle,
  Square,
  Save,
  Play,
  Plus,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Minimize2,
  Trash,
  <PERSON><PERSON><PERSON>,
  Wifi,
  WifiOff,
  Shield,
  Network,
} from 'lucide-react';

// Import custom nodes
import AgentNode from './nodes/AgentNode';
import ToolNode from './nodes/ToolNode';
import ConditionNode from './nodes/ConditionNode';
import StartNode from './nodes/StartNode';
import EndNode from './nodes/EndNode';
import CustomEdge from './edges/CustomEdge';

// Import types and context
import { mockAuthContext } from '@/lib/auth-context';
import { getAPXClient } from '@/lib/apix-client';
import { getSessionManager } from '@/lib/session-manager';

// Define node types
const nodeTypes: NodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  condition: ConditionNode,
  start: StartNode,
  end: EndNode,
};

// Define edge types
const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
};

// Interface for workflow data
interface WorkflowData {
  id: string;
  name: string;
  description: string;
  nodes: any[];
  edges: any[];
  settings: {
    timeout: number;
    retries: number;
    parallelism: number;
    errorHandling: "stop" | "continue" | "retry";
    logging: boolean;
    monitoring: boolean;
  };
  version: string;
  isActive: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  executionCount: number;
  successRate: number;
  avgExecutionTime: number;
  totalCost: number;
}

interface FlowWorkflowBuilderProps {
  workflowId?: string;
  initialData?: WorkflowData;
  mode?: "create" | "edit" | "view" | "debug";
  onSave?: (data: WorkflowData) => Promise<void>;
  onExecute?: (data: WorkflowData, input?: any) => Promise<any>;
  onDelete?: (workflowId: string) => Promise<void>;
}

const FlowWorkflowBuilder = ({
  workflowId,
  initialData,
  mode = "create",
  onSave,
  onExecute,
  onDelete,
}: FlowWorkflowBuilderProps) => {
  // Default workflow data
  const defaultData: WorkflowData = {
    id: workflowId || `workflow-${Date.now()}`,
    name: "New Workflow",
    description: "",
    nodes: [],
    edges: [],
    settings: {
      timeout: 300000, // 5 minutes
      retries: 3,
      parallelism: 1,
      errorHandling: "stop",
      logging: true,
      monitoring: true,
    },
    version: "1.0.0",
    isActive: false,
    isPublic: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: mockAuthContext.user?.id || "unknown",
    executionCount: 0,
    successRate: 0,
    avgExecutionTime: 0,
    totalCost: 0,
  };

  // State for workflow data
  const [workflowData, setWorkflowData] = useState<WorkflowData>(
    initialData || defaultData
  );

  // React Flow state
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLog, setExecutionLog] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  // React Flow instance
  const reactFlowInstance = useReactFlow();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateWorkflow = checkPermission("hybrids", "create");
  const canEditWorkflow = checkPermission("hybrids", "update");
  const canExecuteWorkflow = checkPermission("hybrids", "execute");
  const canDeleteWorkflow = checkPermission("hybrids", "delete");

  // Initialize with default nodes if creating new workflow
  useEffect(() => {
    if (mode === "create" && nodes.length === 0) {
      const initialNodes: Node[] = [
        {
          id: 'start',
          type: 'start',
          position: { x: 250, y: 200 },
          data: { name: 'Start' },
        },
        {
          id: 'end',
          type: 'end',
          position: { x: 750, y: 200 },
          data: { name: 'End' },
        },
      ];
      
      setNodes(initialNodes);
      setWorkflowData(prev => ({
        ...prev,
        nodes: initialNodes,
      }));
    }
  }, [mode, nodes.length, setNodes]);

  // Convert workflow data to React Flow format
  useEffect(() => {
    if (initialData) {
      // Convert nodes
      const flowNodes = (initialData.nodes || []).map(node => ({
        id: node.id,
        type: node.type,
        position: node.position,
        data: {
          name: node.name,
          description: node.description,
          status: node.status,
          config: node.config,
          onConfigure: handleConfigureNode,
        },
      }));

      // Convert edges
      const flowEdges = (initialData.edges || []).map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: 'custom',
        data: {
          label: edge.label,
          condition: edge.condition,
          onDelete: handleDeleteEdge,
        },
      }));

      setNodes(flowNodes);
      setEdges(flowEdges);
    }
  }, [initialData]);

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
  }, []);

  // Handle edge selection
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
  }, []);

  // Handle connection between nodes
  const onConnect = useCallback(
    (connection: Connection) => {
      const newEdge = {
        ...connection,
        id: `edge-${connection.source}-${connection.target}`,
        type: 'custom',
        data: {
          label: '',
          onDelete: handleDeleteEdge,
        },
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setUnsavedChanges(true);
    },
    [setEdges]
  );

  // Handle node configuration
  const handleConfigureNode = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      setSelectedNode(node);
    }
  }, [nodes]);

  // Handle edge deletion
  const handleDeleteEdge = useCallback((edgeId: string) => {
    setEdges(edges => (edges || []).filter(e => e.id !== edgeId));
    setSelectedEdge(null);
    setUnsavedChanges(true);
  }, [setEdges]);

  // Add a new node to the flow
  const addNode = useCallback(
    (type: string) => {
      if (mode === "view") return;
      
      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position: {
          x: Math.random() * 300 + 300,
          y: Math.random() * 300 + 100,
        },
        data: {
          name: `${type.charAt(0).toUpperCase() + type.slice(1)} ${nodes.length}`,
          description: '',
          status: 'idle',
          config: {},
          onConfigure: handleConfigureNode,
        },
      };
      
      setNodes(nds => [...nds, newNode]);
      setUnsavedChanges(true);
    },
    [mode, nodes.length, setNodes, handleConfigureNode]
  );

  // Delete a node from the flow
  const deleteNode = useCallback(
    (nodeId: string) => {
      if (mode === "view") return;
      if (nodeId === 'start' || nodeId === 'end') return;
      
      setNodes(nds => (nds || []).filter(n => n.id !== nodeId));
      setEdges(eds => (eds || []).filter(e => e.source !== nodeId && e.target !== nodeId));
      
      if (selectedNode?.id === nodeId) {
        setSelectedNode(null);
      }
      
      setUnsavedChanges(true);
    },
    [mode, selectedNode, setNodes, setEdges]
  );

  // Update node data
  const updateNodeData = useCallback(
    (nodeId: string, newData: any) => {
      setNodes(nds =>
        nds.map(node => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                ...newData,
                onConfigure: handleConfigureNode,
              },
            };
          }
          return node;
        })
      );
      setUnsavedChanges(true);
    },
    [setNodes, handleConfigureNode]
  );

  // Save workflow
  const handleSave = useCallback(async () => {
    if (!canCreateWorkflow && !canEditWorkflow) {
      setError("You do not have permission to save workflows.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Convert React Flow data to workflow format
      const updatedWorkflow: WorkflowData = {
        ...workflowData,
        name: workflowData.name,
        description: workflowData.description,
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.type,
          name: node.data.name,
          description: node.data.description,
          position: node.position,
          config: node.data.config || {},
          status: node.data.status || 'idle',
        })),
        edges: edges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.data?.label,
          condition: edge.data?.condition,
        })),
        updatedAt: new Date().toISOString(),
      };

      if (onSave) {
        await onSave(updatedWorkflow);
      }

      setWorkflowData(updatedWorkflow);
      setUnsavedChanges(false);
      setExecutionLog(prev => [
        ...prev,
        `${new Date().toLocaleTimeString()} - Workflow saved successfully`,
      ]);
    } catch (error) {
      console.error("Error saving workflow:", error);
      setError(
        `Error saving workflow: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setIsLoading(false);
    }
  }, [
    canCreateWorkflow,
    canEditWorkflow,
    workflowData,
    nodes,
    edges,
    onSave,
  ]);

  // Execute workflow
  const handleExecute = useCallback(async () => {
    if (!canExecuteWorkflow) {
      setError("You do not have permission to execute workflows.");
      return;
    }

    setIsExecuting(true);
    setExecutionLog([
      `${new Date().toLocaleTimeString()} - Starting workflow execution...`,
    ]);
    setError(null);

    try {
      // Convert React Flow data to workflow format for execution
      const executableWorkflow: WorkflowData = {
        ...workflowData,
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.type,
          name: node.data.name,
          description: node.data.description,
          position: node.position,
          config: node.data.config || {},
          status: 'idle',
        })),
        edges: edges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.data?.label,
          condition: edge.data?.condition,
        })),
      };

      // Update node statuses to show execution
      setNodes(nds =>
        nds.map(node => ({
          ...node,
          data: {
            ...node.data,
            status: node.type === 'start' ? 'running' : 'idle',
            onConfigure: handleConfigureNode,
          },
        }))
      );

      if (onExecute) {
        await onExecute(executableWorkflow);
      }

      // Simulate execution for demo purposes
      setTimeout(() => {
        setNodes(nds =>
          nds.map(node => ({
            ...node,
            data: {
              ...node.data,
              status: 'completed',
              onConfigure: handleConfigureNode,
            },
          }))
        );
        
        setIsExecuting(false);
        setExecutionLog(prev => [
          ...prev,
          `${new Date().toLocaleTimeString()} - Workflow executed successfully`,
        ]);
      }, 2000);
    } catch (error) {
      console.error("Error executing workflow:", error);
      setExecutionLog(prev => [
        ...prev,
        `${new Date().toLocaleTimeString()} - Execution failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      ]);
      setError(
        `Execution failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
      setIsExecuting(false);
    }
  }, [
    canExecuteWorkflow,
    workflowData,
    nodes,
    edges,
    onExecute,
    handleConfigureNode,
  ]);

  return (
    <div className="bg-background w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-4 border-b">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">
              {workflowId ? "Edit Workflow" : "Create New Workflow"}
            </h1>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-4 mt-1">
            <Input
              value={workflowData.name}
              onChange={(e) =>
                setWorkflowData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Workflow name..."
              className="w-64"
            />
            <Input
              value={workflowData.description}
              onChange={(e) =>
                setWorkflowData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Description..."
              className="w-96"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={!canCreateWorkflow && !canEditWorkflow || isLoading}
          >
            <Save className="mr-2 h-4 w-4" /> Save Workflow
          </Button>
          <Button
            onClick={handleExecute}
            disabled={isExecuting || !canExecuteWorkflow}
            variant="default"
          >
            {isExecuting ? (
              <>
                <span className="animate-spin mr-2">⏳</span> Executing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" /> Execute
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Node Palette */}
        <div className="w-64 border-r p-4 space-y-4">
          <h3 className="font-semibold">Node Types</h3>
          <div className="space-y-2">
            {[
              {
                type: "agent",
                label: "AI Agent",
                icon: <Bot className="h-4 w-4" />,
              },
              {
                type: "tool",
                label: "Tool",
                icon: <Wrench className="h-4 w-4" />,
              },
              {
                type: "condition",
                label: "Condition",
                icon: <Diamond className="h-4 w-4" />,
              },
            ].map(({ type, label, icon }) => (
              <Button
                key={type}
                variant="outline"
                className="w-full justify-start"
                onClick={() => addNode(type)}
              >
                {icon}
                <span className="ml-2">{label}</span>
              </Button>
            ))}
          </div>

          {/* Execution Log */}
          {executionLog.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Execution Log</h3>
              <ScrollArea className="h-32 border rounded p-2">
                <div className="space-y-1">
                  {executionLog.map((log, index) => (
                    <div key={index} className="text-xs text-muted-foreground">
                      {log}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Selected Node Properties */}
          {selectedNode && (
            <div className="space-y-2 mt-4">
              <h3 className="font-semibold">Node Properties</h3>
              <Card>
                <CardContent className="p-3 space-y-3">
                  <div>
                    <Label htmlFor="nodeName">Name</Label>
                    <Input
                      id="nodeName"
                      value={selectedNode.data.name}
                      onChange={(e) => 
                        updateNodeData(selectedNode.id, { name: e.target.value })
                      }
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="nodeDescription">Description</Label>
                    <Input
                      id="nodeDescription"
                      value={selectedNode.data.description || ''}
                      onChange={(e) => 
                        updateNodeData(selectedNode.id, { description: e.target.value })
                      }
                      className="mt-1"
                    />
                  </div>
                  {selectedNode.id !== 'start' && selectedNode.id !== 'end' && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteNode(selectedNode.id)}
                      className="w-full mt-2"
                    >
                      <Trash className="h-4 w-4 mr-2" /> Delete Node
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Flow Canvas */}
        <div className="flex-1 h-full" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onEdgeClick={onEdgeClick}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            connectionMode={ConnectionMode.Loose}
            fitView
            snapToGrid
            snapGrid={[15, 15]}
            defaultEdgeOptions={{
              type: 'custom',
            }}
          >
            <Background />
            <Controls />
            <MiniMap
              zoomable
              pannable
              nodeColor={(node) => {
                switch (node.type) {
                  case 'agent':
                    return '#93c5fd'; // blue-300
                  case 'tool':
                    return '#86efac'; // green-300
                  case 'condition':
                    return '#fde68a'; // yellow-300
                  case 'start':
                    return '#d1d5db'; // gray-300
                  case 'end':
                    return '#fca5a5'; // red-300
                  default:
                    return '#d1d5db'; // gray-300
                }
              }}
            />
            <div className="absolute top-4 right-4 z-10">
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => reactFlowInstance.zoomIn()}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => reactFlowInstance.zoomOut()}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => reactFlowInstance.fitView()}>
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};

// Wrap with ReactFlowProvider
const FlowWorkflowBuilderWithProvider = (props: FlowWorkflowBuilderProps) => {
  return (
    <ReactFlowProvider>
      <FlowWorkflowBuilder {...props} />
    </ReactFlowProvider>
  );
};

export default FlowWorkflowBuilderWithProvider;
