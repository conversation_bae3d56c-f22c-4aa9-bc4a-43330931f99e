"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Switch } from "../ui/switch";
import { Separator } from "../ui/separator";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import {
  AlertCircle,
  Check,
  ChevronRight,
  Code,
  Cog,
  Copy,
  Play,
  Save,
  Settings,
  Trash,
  Shield,
  Activity,
  Database,
  Wifi,
  WifiOff,
  Plus,
  Minus,
  Key,
  Lock,
  Globe,
  TestTube,
} from "lucide-react";
import { Alert, AlertDescription } from "../ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { getAPXClient } from "../../lib/apix-client";
import { mockAuthContext } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import { createToolExecutedEvent } from "../../lib/apix-events";

interface ToolParameter {
  name: string;
  type: "string" | "number" | "boolean" | "array" | "object";
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: string[];
  };
}

interface ToolCredential {
  name: string;
  type: "api_key" | "bearer_token" | "basic_auth" | "oauth2";
  description: string;
  required: boolean;
  encrypted: boolean;
}

interface ToolData {
  name: string;
  description: string;
  category: string;
  apiEndpoint: string;
  httpMethod: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers: Record<string, string>;
  parameters: ToolParameter[];
  credentials: ToolCredential[];
  responseSchema: any;
  timeout: number;
  retryAttempts: number;
  rateLimiting: {
    enabled: boolean;
    requestsPerMinute: number;
    burstLimit: number;
  };
  caching: {
    enabled: boolean;
    ttl: number;
    keyPattern: string;
  };
  isPublic: boolean;
  version: string;
  tags: string[];
}

interface ToolBuilderProps {
  toolId?: string;
  initialData?: ToolData;
  onSave?: (data: ToolData) => void;
  onTest?: (data: ToolData) => void;
}

const ToolBuilder: React.FC<ToolBuilderProps> = ({
  toolId,
  initialData,
  onSave = () => {},
  onTest = () => {},
}) => {
  const defaultData: ToolData = {
    name: "",
    description: "",
    category: "api",
    apiEndpoint: "",
    httpMethod: "GET",
    headers: { "Content-Type": "application/json" },
    parameters: [],
    credentials: [],
    responseSchema: {},
    timeout: 30000,
    retryAttempts: 3,
    rateLimiting: {
      enabled: false,
      requestsPerMinute: 60,
      burstLimit: 10,
    },
    caching: {
      enabled: false,
      ttl: 300,
      keyPattern: "{{endpoint}}-{{params}}",
    },
    isPublic: false,
    version: "1.0.0",
    tags: [],
  };

  const [toolData, setToolData] = useState<ToolData>(
    initialData || defaultData,
  );
  const [activeTab, setActiveTab] = useState("basic");
  const [isTesting, setIsTesting] = useState(false);
  const [testInput, setTestInput] = useState("{}");
  const [testResponse, setTestResponse] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [newTag, setNewTag] = useState("");

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateTool = checkPermission("tools", "create");
  const canEditTool = checkPermission("tools", "update");
  const canDeleteTool = checkPermission("tools", "delete");
  const canTestTool = checkPermission("tools", "execute");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Quota enforcement
  const quotaUsage = organization?.usage || {
    agents: 0,
    executions: 0,
    storage: 0,
    apiCalls: 0,
  };
  const quotaLimits = organization?.quotas || {
    agents: 100,
    executions: 10000,
    storage: 1000000000,
    apiCalls: 50000,
  };

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to tool events
          apixClient.subscribe(
            "tools",
            ["created", "updated", "deleted", "executed"],
            handleRealtimeEvent,
          );

          // Create session for this tool builder instance
          const newSessionId = await sessionManager.createSession(
            "tools",
            "user",
            { toolId, builderState: toolData },
            {
              tags: ["tool-builder"],
              conversationId: `builder-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization]);

  const handleRealtimeEvent = useCallback(
    (event: any) => {
      // Handle real-time tool events
      switch (event.type) {
        case "updated":
          if (event.data.toolId === toolId) {
            console.log("Tool updated by another user:", event.data);
          }
          break;
        case "executed":
          if (event.data.toolId === toolId) {
            setTestResponse(JSON.stringify(event.data.output, null, 2));
            setIsTesting(false);
          }
          break;
      }
    },
    [toolId],
  );

  const handleInputChange = (field: keyof ToolData, value: any) => {
    setToolData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Update session with new state
      if (sessionId) {
        sessionManager.updateSession(sessionId, { builderState: newData });
      }

      return newData;
    });
  };

  const addParameter = () => {
    const newParam: ToolParameter = {
      name: "",
      type: "string",
      description: "",
      required: false,
    };
    handleInputChange("parameters", [...toolData.parameters, newParam]);
  };

  const updateParameter = (
    index: number,
    field: keyof ToolParameter,
    value: any,
  ) => {
    const updatedParams = [...toolData.parameters];
    updatedParams[index] = { ...updatedParams[index], [field]: value };
    handleInputChange("parameters", updatedParams);
  };

  const removeParameter = (index: number) => {
    const updatedParams = toolData.parameters.filter((_, i) => i !== index);
    handleInputChange("parameters", updatedParams);
  };

  const addCredential = () => {
    const newCred: ToolCredential = {
      name: "",
      type: "api_key",
      description: "",
      required: true,
      encrypted: true,
    };
    handleInputChange("credentials", [...toolData.credentials, newCred]);
  };

  const updateCredential = (
    index: number,
    field: keyof ToolCredential,
    value: any,
  ) => {
    const updatedCreds = [...toolData.credentials];
    updatedCreds[index] = { ...updatedCreds[index], [field]: value };
    handleInputChange("credentials", updatedCreds);
  };

  const removeCredential = (index: number) => {
    const updatedCreds = toolData.credentials.filter((_, i) => i !== index);
    handleInputChange("credentials", updatedCreds);
  };

  const addTag = () => {
    if (newTag.trim() && !toolData.tags.includes(newTag.trim())) {
      handleInputChange("tags", [...toolData.tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tag: string) => {
    handleInputChange(
      "tags",
      toolData.tags.filter((t) => t !== tag),
    );
  };

  const handleSave = async () => {
    if (!canCreateTool && !canEditTool) {
      alert("You do not have permission to save tools.");
      return;
    }

    setIsSaving(true);
    try {
      if (!toolData.name.trim()) {
        throw new Error("Tool name is required");
      }
      if (!toolData.apiEndpoint.trim()) {
        throw new Error("API endpoint is required");
      }

      // Prepare API request data
      const requestData = {
        name: toolData.name,
        description: toolData.description,
        category: toolData.category,
        apiEndpoint: toolData.apiEndpoint,
        httpMethod: toolData.httpMethod,
        headers: toolData.headers,
        parameters: toolData.parameters,
        credentials: toolData.credentials,
        responseSchema: toolData.responseSchema,
        timeout: toolData.timeout,
        retryAttempts: toolData.retryAttempts,
        rateLimiting: toolData.rateLimiting,
        caching: toolData.caching,
        isPublic: toolData.isPublic,
        version: toolData.version,
        tags: toolData.tags,
        metadata: {
          createdVia: "tool-builder",
          sessionId,
          builderVersion: "2.0.0",
        },
      };

      // Make API call to save tool
      const apiUrl = toolId ? `/api/tools/${toolId}` : "/api/tools";
      const method = toolId ? "PUT" : "POST";

      const response = await fetch(apiUrl, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save tool");
      }

      const result = await response.json();
      const savedTool = result.data.tool;
      const apiSessionId = result.data.sessionId;

      // Update local state with saved tool data
      setToolData({
        ...toolData,
        ...savedTool,
      });

      // Update session with save confirmation
      if (sessionId) {
        await sessionManager.updateSession(sessionId, {
          builderState: savedTool,
          lastSaved: new Date().toISOString(),
          apiSessionId,
          savedToolId: savedTool.id,
        });
      }

      // Broadcast save event via APIX
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `tool-saved-${savedTool.id}`,
          type: toolId ? "tool_updated" : "tool_created",
          module: "tools",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            toolId: savedTool.id,
            tool: savedTool,
            sessionId: apiSessionId,
          },
          version: 1,
        });
      }

      onSave(savedTool);
      alert(`Tool ${toolId ? "updated" : "created"} successfully!`);
    } catch (error) {
      console.error("Error saving tool:", error);
      alert(
        `Error saving tool: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    if (!canTestTool) {
      alert("You do not have permission to test tools.");
      return;
    }

    if (!testInput.trim()) {
      alert("Please enter test input");
      return;
    }

    setIsTesting(true);
    setTestResponse("");

    try {
      // Validate JSON input
      let parsedInput;
      try {
        parsedInput = JSON.parse(testInput);
      } catch (e) {
        throw new Error("Invalid JSON in test input");
      }

      const testSessionId = await sessionManager.createSession(
        "tools",
        "tool",
        {
          toolConfig: toolData,
          testInput: parsedInput,
          startTime: new Date().toISOString(),
        },
        {
          parentSessionId: sessionId,
          tags: ["test", "tool-execution"],
          conversationId: `test-${Date.now()}`,
        },
      );

      // Broadcast test start event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `tool-test-start-${Date.now()}`,
          type: "tool_execution_started",
          module: "tools",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            toolId: toolId || "new-tool",
            testSessionId,
            input: parsedInput,
            config: toolData,
          },
          version: 1,
        });
      }

      // Make real API call to test endpoint
      const testStartTime = Date.now();
      const response = await fetch("/api/tools/execute", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
        body: JSON.stringify({
          toolId: toolId,
          input: parsedInput,
          metadata: {
            testSessionId,
            builderVersion: "2.0.0",
            testMode: true,
          },
        }),
      });

      const testEndTime = Date.now();
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Test failed");
      }

      if (result.success) {
        setTestResponse(JSON.stringify(result.data, null, 2));

        // Update test session with results
        await sessionManager.updateSession(testSessionId, {
          response: result.data.output,
          cost: result.data.cost,
          duration: result.data.duration,
          endTime: new Date().toISOString(),
          success: true,
        });

        // Broadcast test completion
        if (isConnected && user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          const executionEvent = createToolExecutedEvent(
            organization.id,
            user.id,
            {
              toolId: toolId || "new-tool",
              executionId: result.data.executionId,
              input: parsedInput,
              output: result.data.output,
              duration: result.data.duration,
              success: true,
              cost: result.data.cost,
            },
          );
          apixClient.sendEvent(executionEvent);
        }
      } else {
        throw new Error(result.error || "Test failed");
      }
    } catch (error) {
      console.error("Error testing tool:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An error occurred while testing the tool.";
      const errorResponse = {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      };
      setTestResponse(JSON.stringify(errorResponse, null, 2));

      // Broadcast test error
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `tool-test-error-${Date.now()}`,
          type: "tool_execution_failed",
          module: "tools",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            toolId: toolId || "new-tool",
            error: errorMessage,
          },
          version: 1,
        });
      }
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="bg-background w-full h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">
              {toolId ? "Edit Tool" : "Create New Tool"}
            </h1>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    {isConnected ? (
                      <Wifi className="h-4 w-4 text-green-500" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-500" />
                    )}
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {isConnected
                        ? "Connected to APIX"
                        : "Disconnected from APIX"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>
              <Badge variant="secondary">{organization?.name}</Badge>
            </div>
          </div>
          <p className="text-muted-foreground mt-1">
            {toolId ? `Editing tool ${toolId}` : "Configure your new API tool"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => {}}
            disabled={!canCreateTool}
          >
            <Copy className="mr-2 h-4 w-4" /> Duplicate
          </Button>
          <Button
            variant="destructive"
            onClick={() => {}}
            disabled={!canDeleteTool}
          >
            <Trash className="mr-2 h-4 w-4" /> Delete
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || (!canCreateTool && !canEditTool)}
          >
            {isSaving ? (
              <>
                <span className="animate-spin mr-2">⏳</span> Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" /> Save Tool
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <div className="w-2/3 p-4 overflow-y-auto">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="mb-4">
              <TabsTrigger value="basic">
                <Settings className="mr-2 h-4 w-4" /> Basic
              </TabsTrigger>
              <TabsTrigger value="api">
                <Globe className="mr-2 h-4 w-4" /> API Config
              </TabsTrigger>
              <TabsTrigger value="security">
                <Lock className="mr-2 h-4 w-4" /> Security
              </TabsTrigger>
              <TabsTrigger value="advanced">
                <Cog className="mr-2 h-4 w-4" /> Advanced
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>
                    Configure the basic settings for your tool.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Tool Name</Label>
                      <Input
                        id="name"
                        value={toolData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        placeholder="My API Tool"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="version">Version</Label>
                      <Input
                        id="version"
                        value={toolData.version}
                        onChange={(e) =>
                          handleInputChange("version", e.target.value)
                        }
                        placeholder="1.0.0"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={toolData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      placeholder="Describe what this tool does..."
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={toolData.category}
                        onValueChange={(value) =>
                          handleInputChange("category", value)
                        }
                      >
                        <SelectTrigger id="category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="api">API Integration</SelectItem>
                          <SelectItem value="database">Database</SelectItem>
                          <SelectItem value="email">Email</SelectItem>
                          <SelectItem value="file">File Processing</SelectItem>
                          <SelectItem value="analytics">Analytics</SelectItem>
                          <SelectItem value="communication">
                            Communication
                          </SelectItem>
                          <SelectItem value="utility">Utility</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center space-x-2 pt-6">
                      <Switch
                        id="isPublic"
                        checked={toolData.isPublic}
                        onCheckedChange={(checked) =>
                          handleInputChange("isPublic", checked)
                        }
                      />
                      <Label htmlFor="isPublic">Make this tool public</Label>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {toolData.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {tag}
                          <button
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:text-destructive"
                          >
                            <Minus className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add tag..."
                        onKeyPress={(e) => e.key === "Enter" && addTag()}
                      />
                      <Button onClick={addTag} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="api" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>API Configuration</CardTitle>
                  <CardDescription>
                    Configure the API endpoint and parameters.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="httpMethod">HTTP Method</Label>
                      <Select
                        value={toolData.httpMethod}
                        onValueChange={(value: any) =>
                          handleInputChange("httpMethod", value)
                        }
                      >
                        <SelectTrigger id="httpMethod">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="GET">GET</SelectItem>
                          <SelectItem value="POST">POST</SelectItem>
                          <SelectItem value="PUT">PUT</SelectItem>
                          <SelectItem value="DELETE">DELETE</SelectItem>
                          <SelectItem value="PATCH">PATCH</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="col-span-2 space-y-2">
                      <Label htmlFor="apiEndpoint">API Endpoint</Label>
                      <Input
                        id="apiEndpoint"
                        value={toolData.apiEndpoint}
                        onChange={(e) =>
                          handleInputChange("apiEndpoint", e.target.value)
                        }
                        placeholder="https://api.example.com/endpoint"
                      />
                    </div>
                  </div>

                  {/* Parameters */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label>Parameters</Label>
                      <Button onClick={addParameter} size="sm">
                        <Plus className="mr-2 h-4 w-4" /> Add Parameter
                      </Button>
                    </div>
                    <ScrollArea className="max-h-64">
                      <div className="space-y-3">
                        {toolData.parameters.map((param, index) => (
                          <Card key={index} className="p-4">
                            <div className="grid grid-cols-4 gap-3">
                              <Input
                                placeholder="Parameter name"
                                value={param.name}
                                onChange={(e) =>
                                  updateParameter(index, "name", e.target.value)
                                }
                              />
                              <Select
                                value={param.type}
                                onValueChange={(value: any) =>
                                  updateParameter(index, "type", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="string">String</SelectItem>
                                  <SelectItem value="number">Number</SelectItem>
                                  <SelectItem value="boolean">
                                    Boolean
                                  </SelectItem>
                                  <SelectItem value="array">Array</SelectItem>
                                  <SelectItem value="object">Object</SelectItem>
                                </SelectContent>
                              </Select>
                              <Input
                                placeholder="Description"
                                value={param.description}
                                onChange={(e) =>
                                  updateParameter(
                                    index,
                                    "description",
                                    e.target.value,
                                  )
                                }
                              />
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={param.required}
                                  onCheckedChange={(checked) =>
                                    updateParameter(index, "required", checked)
                                  }
                                />
                                <Label className="text-xs">Required</Label>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeParameter(index)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Security & Credentials</CardTitle>
                  <CardDescription>
                    Configure authentication and security settings.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <Label>Credentials</Label>
                    <Button onClick={addCredential} size="sm">
                      <Plus className="mr-2 h-4 w-4" /> Add Credential
                    </Button>
                  </div>
                  <ScrollArea className="max-h-64">
                    <div className="space-y-3">
                      {toolData.credentials.map((cred, index) => (
                        <Card key={index} className="p-4">
                          <div className="grid grid-cols-3 gap-3">
                            <Input
                              placeholder="Credential name"
                              value={cred.name}
                              onChange={(e) =>
                                updateCredential(index, "name", e.target.value)
                              }
                            />
                            <Select
                              value={cred.type}
                              onValueChange={(value: any) =>
                                updateCredential(index, "type", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="api_key">API Key</SelectItem>
                                <SelectItem value="bearer_token">
                                  Bearer Token
                                </SelectItem>
                                <SelectItem value="basic_auth">
                                  Basic Auth
                                </SelectItem>
                                <SelectItem value="oauth2">OAuth2</SelectItem>
                              </SelectContent>
                            </Select>
                            <Input
                              placeholder="Description"
                              value={cred.description}
                              onChange={(e) =>
                                updateCredential(
                                  index,
                                  "description",
                                  e.target.value,
                                )
                              }
                            />
                          </div>
                          <div className="flex items-center justify-between mt-3">
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={cred.required}
                                  onCheckedChange={(checked) =>
                                    updateCredential(index, "required", checked)
                                  }
                                />
                                <Label className="text-xs">Required</Label>
                              </div>
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={cred.encrypted}
                                  onCheckedChange={(checked) =>
                                    updateCredential(
                                      index,
                                      "encrypted",
                                      checked,
                                    )
                                  }
                                />
                                <Label className="text-xs">Encrypted</Label>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeCredential(index)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Settings</CardTitle>
                  <CardDescription>
                    Configure performance and reliability settings.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="timeout">Timeout (ms)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={toolData.timeout}
                        onChange={(e) =>
                          handleInputChange("timeout", parseInt(e.target.value))
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="retryAttempts">Retry Attempts</Label>
                      <Input
                        id="retryAttempts"
                        type="number"
                        value={toolData.retryAttempts}
                        onChange={(e) =>
                          handleInputChange(
                            "retryAttempts",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Rate Limiting */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="rateLimitingEnabled"
                        checked={toolData.rateLimiting.enabled}
                        onCheckedChange={(checked) =>
                          handleInputChange("rateLimiting", {
                            ...toolData.rateLimiting,
                            enabled: checked,
                          })
                        }
                      />
                      <Label htmlFor="rateLimitingEnabled">
                        Enable Rate Limiting
                      </Label>
                    </div>
                    {toolData.rateLimiting.enabled && (
                      <div className="grid grid-cols-2 gap-4 ml-6">
                        <div className="space-y-2">
                          <Label>Requests per Minute</Label>
                          <Input
                            type="number"
                            value={toolData.rateLimiting.requestsPerMinute}
                            onChange={(e) =>
                              handleInputChange("rateLimiting", {
                                ...toolData.rateLimiting,
                                requestsPerMinute: parseInt(e.target.value),
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Burst Limit</Label>
                          <Input
                            type="number"
                            value={toolData.rateLimiting.burstLimit}
                            onChange={(e) =>
                              handleInputChange("rateLimiting", {
                                ...toolData.rateLimiting,
                                burstLimit: parseInt(e.target.value),
                              })
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Caching */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="cachingEnabled"
                        checked={toolData.caching.enabled}
                        onCheckedChange={(checked) =>
                          handleInputChange("caching", {
                            ...toolData.caching,
                            enabled: checked,
                          })
                        }
                      />
                      <Label htmlFor="cachingEnabled">Enable Caching</Label>
                    </div>
                    {toolData.caching.enabled && (
                      <div className="grid grid-cols-2 gap-4 ml-6">
                        <div className="space-y-2">
                          <Label>TTL (seconds)</Label>
                          <Input
                            type="number"
                            value={toolData.caching.ttl}
                            onChange={(e) =>
                              handleInputChange("caching", {
                                ...toolData.caching,
                                ttl: parseInt(e.target.value),
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Cache Key Pattern</Label>
                          <Input
                            value={toolData.caching.keyPattern}
                            onChange={(e) =>
                              handleInputChange("caching", {
                                ...toolData.caching,
                                keyPattern: e.target.value,
                              })
                            }
                            placeholder="{{endpoint}}-{{params}}"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/3 border-l p-4 overflow-y-auto">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                Test Your Tool
              </CardTitle>
              <CardDescription>
                Test your tool with sample input to verify it works correctly.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="testInput">Test Input (JSON)</Label>
                  <Textarea
                    id="testInput"
                    value={testInput}
                    onChange={(e) => setTestInput(e.target.value)}
                    placeholder='{"param1": "value1", "param2": "value2"}'
                    rows={6}
                    className="font-mono"
                  />
                </div>

                {testResponse && (
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label className="flex-1">Response</Label>
                      <Badge variant="outline" className="ml-2">
                        {toolData.httpMethod} {toolData.category}
                      </Badge>
                    </div>
                    <ScrollArea className="bg-muted p-4 rounded-md font-mono text-sm max-h-[400px]">
                      <pre className="whitespace-pre-wrap">{testResponse}</pre>
                    </ScrollArea>
                  </div>
                )}

                {!testResponse && !isTesting && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Configure your tool settings and click "Test Tool" to see
                      a response.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <div className="w-full space-y-2">
                <Button
                  onClick={handleTest}
                  disabled={isTesting || !canTestTool}
                  className="w-full"
                >
                  {isTesting ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span> Testing...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" /> Test Tool
                    </>
                  )}
                </Button>

                {sessionId && (
                  <div className="text-xs text-muted-foreground text-center">
                    Session: {sessionId.split("_").pop()}
                  </div>
                )}

                {!canTestTool && (
                  <div className="text-xs text-red-500 text-center">
                    No permission to test tools
                  </div>
                )}
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ToolBuilder;
