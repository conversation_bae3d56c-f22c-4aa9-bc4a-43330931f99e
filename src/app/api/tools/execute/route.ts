// Production API Route for Tool Execution
// Real API integration with comprehensive monitoring and agent context preservation

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";
import { createToolExecutedEvent } from "@/lib/apix-events";

interface ExecuteToolRequest {
  toolId: string;
  input: Record<string, any>;
  agentId?: string;
  workflowId?: string;
  context?: Record<string, any>;
  metadata?: Record<string, any>;
}

interface ExecuteToolResponse {
  success: boolean;
  data?: {
    executionId: string;
    output: any;
    duration: number;
    cost: number;
    sessionId: string;
    agentContext?: any;
    workflowContext?: any;
  };
  error?: string;
}

// POST /api/tools/execute - Execute tool with agent/workflow context
export async function POST(
  request: NextRequest,
): Promise<NextResponse<ExecuteToolResponse>> {
  const startTime = Date.now();
  let executionId: string | null = null;
  let sessionId: string | null = null;

  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "tools", "execute")) {
      return NextResponse.json(
        { success: false, error: "Insufficient permissions to execute tools" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body: ExecuteToolRequest = await request.json();

    if (!body.toolId?.trim()) {
      return NextResponse.json(
        { success: false, error: "Tool ID is required" },
        { status: 400 },
      );
    }

    if (!body.input || typeof body.input !== "object") {
      return NextResponse.json(
        { success: false, error: "Input object is required" },
        { status: 400 },
      );
    }

    // Get tool configuration
    const tool = await db.getTool(body.toolId, payload.org);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: "Tool not found" },
        { status: 404 },
      );
    }

    if (tool.status !== "active") {
      return NextResponse.json(
        { success: false, error: "Tool is not active" },
        { status: 400 },
      );
    }

    // Create session for this execution
    const sessionManager = getSessionManager(payload.org);
    sessionId = await sessionManager.createSession(
      "tools",
      "tool",
      {
        action: "execute",
        toolId: body.toolId,
        toolConfig: tool,
        input: body.input,
        agentId: body.agentId,
        workflowId: body.workflowId,
        context: body.context,
        startTime: new Date().toISOString(),
      },
      {
        tags: ["api", "tools", "execute", "execution"],
        conversationId: `execute-${Date.now()}`,
        ...(body.agentId && { parentSessionId: `agent-${body.agentId}` }),
        ...(body.workflowId && {
          parentSessionId: `workflow-${body.workflowId}`,
        }),
      },
    );

    // Create execution record
    const executionData = {
      toolId: body.toolId,
      organizationId: payload.org,
      userId: payload.sub,
      input: body.input,
      output: null,
      status: "running" as const,
      cost: 0,
      duration: 0,
      agentId: body.agentId,
      workflowId: body.workflowId,
      context: body.context || {},
      metadata: {
        ...body.metadata,
        sessionId,
        executionMode: body.agentId
          ? "agent"
          : body.workflowId
            ? "workflow"
            : "direct",
        toolConfig: tool,
      },
    };

    const execution = await db.createToolExecution(executionData);
    executionId = execution.id;

    // Send execution start event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `tool-execution-start-${executionId}`,
        type: "tool_execution_started",
        module: "tools",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          executionId,
          toolId: body.toolId,
          tool,
          input: body.input,
          agentId: body.agentId,
          workflowId: body.workflowId,
          context: body.context,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX start event:", apixError);
    }

    // Validate input parameters
    const validationErrors = validateToolInput(tool, body.input);
    if (validationErrors.length > 0) {
      throw new Error(
        `Input validation failed: ${validationErrors.join(", ")}`,
      );
    }

    // Prepare API request
    const apiRequest = await prepareAPIRequest(tool, body.input, body.context);

    // Execute the tool with retry logic
    const apiResponse = await executeWithRetry(
      apiRequest,
      tool.retryAttempts || 3,
      tool.timeout || 30000,
    );

    const totalDuration = Date.now() - startTime;
    const executionCost = calculateExecutionCost(tool, apiResponse);

    // Update execution record with results
    const updatedExecution = await db.updateToolExecution(
      executionId,
      payload.org,
      {
        output: apiResponse.data,
        status: "completed",
        duration: totalDuration,
        cost: executionCost,
        metadata: {
          ...executionData.metadata,
          apiResponse: {
            status: apiResponse.status,
            headers: apiResponse.headers,
            duration: apiResponse.duration,
          },
          finishReason: "success",
          totalDuration,
        },
      },
    );

    // Update session with results
    await sessionManager.updateSession(sessionId, {
      output: apiResponse.data,
      cost: executionCost,
      duration: totalDuration,
      endTime: new Date().toISOString(),
      success: true,
    });

    // Update tool usage statistics
    try {
      await db.updateToolUsage(body.toolId, payload.org, executionCost);
    } catch (usageError) {
      console.warn("Failed to update tool usage:", usageError);
    }

    // Send execution completion event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      const executionEvent = createToolExecutedEvent(payload.org, payload.sub, {
        toolId: body.toolId,
        executionId,
        input: body.input,
        output: apiResponse.data,
        duration: totalDuration,
        success: true,
        cost: executionCost,
      });

      // Add agent/workflow context to event
      if (body.agentId || body.workflowId) {
        executionEvent.data = {
          ...executionEvent.data,
          agentId: body.agentId,
          workflowId: body.workflowId,
          context: body.context,
          integrationMode: body.agentId ? "agent" : "workflow",
        };
      }

      await apixClient.sendEvent(executionEvent);
    } catch (apixError) {
      console.warn("Failed to send APIX completion event:", apixError);
    }

    // Prepare response with context preservation
    const responseData = {
      executionId,
      output: apiResponse.data,
      duration: totalDuration,
      cost: executionCost,
      sessionId,
    };

    // Add agent context if this was called by an agent
    if (body.agentId) {
      responseData.agentContext = {
        agentId: body.agentId,
        preservedContext: body.context,
        toolResult: apiResponse.data,
        executionMetadata: {
          toolName: tool.name,
          duration: totalDuration,
          cost: executionCost,
        },
      };
    }

    // Add workflow context if this was called by a workflow
    if (body.workflowId) {
      responseData.workflowContext = {
        workflowId: body.workflowId,
        stepContext: body.context,
        toolResult: apiResponse.data,
        executionMetadata: {
          toolName: tool.name,
          duration: totalDuration,
          cost: executionCost,
        },
      };
    }

    return NextResponse.json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    console.error("Error executing tool:", error);

    // Update execution record with error if it was created
    if (executionId && payload) {
      try {
        await db.updateToolExecution(executionId, payload.org, {
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          duration: Date.now() - startTime,
        });
      } catch (updateError) {
        console.error("Failed to update execution with error:", updateError);
      }
    }

    // Update session with error if it was created
    if (sessionId && payload) {
      try {
        const sessionManager = getSessionManager(payload.org);
        await sessionManager.updateSession(sessionId, {
          error: error instanceof Error ? error.message : "Unknown error",
          endTime: new Date().toISOString(),
          success: false,
        });
      } catch (sessionError) {
        console.error("Failed to update session with error:", sessionError);
      }
    }

    // Send execution error event via APIX
    if (payload) {
      try {
        const apixClient = getAPXClient(
          payload.org,
          payload.sub,
          request.headers.get("authorization")!.substring(7),
        );
        await apixClient.sendEvent({
          id: `tool-execution-error-${executionId || Date.now()}`,
          type: "tool_execution_failed",
          module: "tools",
          organizationId: payload.org,
          userId: payload.sub,
          timestamp: new Date().toISOString(),
          data: {
            executionId,
            error: error instanceof Error ? error.message : "Unknown error",
            sessionId,
          },
          version: 1,
        });
      } catch (apixError) {
        console.warn("Failed to send APIX error event:", apixError);
      }
    }

    // Determine appropriate error response
    if (error instanceof Error) {
      if (error.message.includes("Rate limit")) {
        return NextResponse.json(
          {
            success: false,
            error: "Rate limit exceeded. Please try again later.",
          },
          { status: 429 },
        );
      }
      if (error.message.includes("Timeout")) {
        return NextResponse.json(
          {
            success: false,
            error: "Tool execution timeout. Please try again.",
          },
          { status: 408 },
        );
      }
      if (error.message.includes("validation")) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 },
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: "Tool execution failed",
      },
      { status: 500 },
    );
  }
}

// Helper functions
function validateToolInput(tool: any, input: Record<string, any>): string[] {
  const errors: string[] = [];

  for (const param of tool.parameters || []) {
    if (param.required && !(param.name in input)) {
      errors.push(`Required parameter '${param.name}' is missing`);
    }

    if (param.name in input) {
      const value = input[param.name];

      // Type validation
      if (param.type === "number" && typeof value !== "number") {
        errors.push(`Parameter '${param.name}' must be a number`);
      }
      if (param.type === "boolean" && typeof value !== "boolean") {
        errors.push(`Parameter '${param.name}' must be a boolean`);
      }
      if (param.type === "string" && typeof value !== "string") {
        errors.push(`Parameter '${param.name}' must be a string`);
      }

      // Validation rules
      if (param.validation) {
        if (
          param.validation.min !== undefined &&
          value < param.validation.min
        ) {
          errors.push(
            `Parameter '${param.name}' must be at least ${param.validation.min}`,
          );
        }
        if (
          param.validation.max !== undefined &&
          value > param.validation.max
        ) {
          errors.push(
            `Parameter '${param.name}' must be at most ${param.validation.max}`,
          );
        }
        if (
          param.validation.pattern &&
          typeof value === "string" &&
          !new RegExp(param.validation.pattern).test(value)
        ) {
          errors.push(
            `Parameter '${param.name}' does not match required pattern`,
          );
        }
        if (param.validation.enum && !param.validation.enum.includes(value)) {
          errors.push(
            `Parameter '${param.name}' must be one of: ${param.validation.enum.join(", ")}`,
          );
        }
      }
    }
  }

  return errors;
}

async function prepareAPIRequest(
  tool: any,
  input: Record<string, any>,
  context?: Record<string, any>,
) {
  // Replace parameter placeholders in endpoint
  let endpoint = tool.apiEndpoint;
  for (const [key, value] of Object.entries(input)) {
    endpoint = endpoint.replace(new RegExp(`{{${key}}}`, "g"), String(value));
  }

  // Prepare headers
  const headers = { ...tool.headers };

  // Add authentication headers based on credentials
  for (const credential of tool.credentials || []) {
    if (credential.required) {
      // In production, retrieve actual credential values from secure storage
      switch (credential.type) {
        case "api_key":
          headers["X-API-Key"] = "mock-api-key";
          break;
        case "bearer_token":
          headers["Authorization"] = "Bearer mock-token";
          break;
        case "basic_auth":
          headers["Authorization"] = "Basic " + btoa("user:pass");
          break;
      }
    }
  }

  return {
    url: endpoint,
    method: tool.httpMethod,
    headers,
    body: tool.httpMethod !== "GET" ? JSON.stringify(input) : undefined,
  };
}

async function executeWithRetry(
  request: any,
  maxRetries: number,
  timeout: number,
): Promise<any> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const startTime = Date.now();
      const response = await fetch(request.url, {
        method: request.method,
        headers: request.headers,
        body: request.body,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      const duration = Date.now() - startTime;

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      return {
        data,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        duration,
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error("Unknown error");

      if (attempt < maxRetries) {
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error("Tool execution failed after all retries");
}

function calculateExecutionCost(tool: any, response: any): number {
  // Simple cost calculation - in production this would be more sophisticated
  const baseCost = 0.01; // $0.01 base cost
  const durationCost = (response.duration / 1000) * 0.001; // $0.001 per second
  return baseCost + durationCost;
}
